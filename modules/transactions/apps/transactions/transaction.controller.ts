import { injectable } from "tsyringe";
import TransactionService from "./transaction.service";
import { Request, Response } from "express";
import { OpenAPI } from "../../../common/lib/decorators";
import { GetTransactionsResponseSchema } from "./responses";

@injectable()
export default class TransactionController {
    constructor(private _service: TransactionService) {}

    @OpenAPI(
        "transaction",
        "/",
        "get",
        undefined,
        undefined,
        GetTransactionsResponseSchema,
        "bearerAuth",
    )
    getUserTransactions = async (req: Request, res: Response) => {
        const { user, parsedQuery } = req;

        const transactions = await this._service.getUserTransactions(
            user!.id,
            parsedQuery,
        );

        res.success(transactions);
    };
}
