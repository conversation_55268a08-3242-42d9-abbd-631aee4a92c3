import { inject, injectable, registry } from "tsyringe";
import { addNotificationDto } from "./types/dtos";
import NotificationRepo from "./notification.repo";
import { FcmService } from "../../../common/lib/notifications/fcm";
import { Job, Processor } from "bullmq";
import { RedisConnection } from "../../../common/lib/redis";
import { BullMQModule, createJobProcessor } from "../../../common/lib/bullmq";
import { NotificationJob } from "./types";
import { utils } from "../../../common";
import { registries } from "./registries";
import NotificationConfigService from "../notifications-config/notifications-config.service";
import { UserLanguageService } from "../../../common/lib/services/user-language.service";

@registry(registries)
@injectable()
export default class NotificationService {
    private _redisConnection: RedisConnection;
    private _notificationQueue: BullMQModule<NotificationJob[]>;
    private _notificationJobName: string = "notify";

    constructor(
        private _repo: NotificationRepo,
        private _notificationConfigService: NotificationConfigService,
        private _fcmService: FcmService,
        private _userLanguageService: UserLanguageService,
        @inject("queue")
        createQueueModule: <T>(
            queueName: string,
            processor: Processor<T>,
            redisConnection: RedisConnection,
        ) => BullMQModule<T>,
        @inject("redis")
        createRedisConnection: () => RedisConnection,
    ) {
        this._redisConnection = createRedisConnection();

        const notificationProcessor: Processor<NotificationJob[]> =
            createJobProcessor(this._sendNotification.bind(this));
        this._notificationQueue = createQueueModule(
            this._notificationJobName,
            notificationProcessor,
            this._redisConnection,
        );
    }

    private _sendNotification = async (job: Job<NotificationJob[]>) => {
        await this._fcmService.sendNotification(job.data);
    };

    private _addNotification = async (args: addNotificationDto) => {
        console.log(`_addNotification`, args);

        try {
            const tokens = await this._notificationConfigService.getUserTokens(
                args.userId,
            );
            await this._repo.create(args);

            await this._notificationQueue.addJob(
                this._notificationJobName,
                tokens.map(({ token }) => ({
                    data: {
                        ...(utils.isNotNil(args.sourceId)
                            ? { sourceId: String(args.sourceId) }
                            : {}),
                        type: String(args.type),
                    },
                    notification: {
                        title: args.title,
                        body: args.body,
                    },
                    token,
                })),
            );
        } catch (e) {
            console.error(`Error adding notification`, e);
        }
    };

    getNotifications = async (profile: Express.User) => {
        const { id: userId } = profile;

        await this._repo.findByQuery({ userId });
    };

    addLowStockNotification = async (userId: number, inventoryId: number) => {
        await this._addNotification({
            title: "Low Stock",
            body: "Low stock",
            type: "inventory",
            userId,
            sourceId: inventoryId,
        });
    };

    addNewOrderNotification = async (userId: number, orderId: number) => {
        await this._addNotification({
            title: "New order",
            body: "You have a new order",
            type: "order",
            userId,
            sourceId: orderId,
        });
    };
    addDirectChatNotification = async (user: { id: number; name: string }) => {
        await this._addNotification({
            title: "Direct Message from " + user.name,
            body: "User " + user.name + " sent you a direct message",
            type: "direct_chat",
            userId: user.id,
        });
    };
}
