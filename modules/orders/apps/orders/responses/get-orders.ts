import { JSONSchemaType } from "ajv";
import { OrdersResponse } from "../types";
import { OrderItemResponseSchema } from "../../../../order-items/apps/order-items/responses";
import { ExchangeRateType, OrderStatus } from "../types/enums";

export const GetOrdersResponseSchema: JSONSchemaType<OrdersResponse[]> = {
    type: "array",
    items: {
        type: "object",
        prefix: "order_",
        properties: {
            id: {
                type: "number",
            },
            origin: {
                type: "string",
            },
            destination: {
                type: "string",
            },
            postalCode: {
                type: "string",
            },
            receiver: {
                type: "string",
            },
            clientId: {
                type: "number",
            },
            price: {
                type: "string",
            },
            cost: {
                type: "string",
            },
            profit: {
                type: "string",
            },
            shippingPrice: {
                type: "string",
            },
            status: {
                type: "string",
                enum: Object.values(OrderStatus),
            },
            exchangeRateType: {
                type: "string",
                enum: Object.values(ExchangeRateType),
            },
            exchangeRateValue: {
                type: "number",
            },
            createdAt: {
                type: "string",
            },
            items: OrderItemResponseSchema,
        },
        required: [],
    },
};
