import { singleton } from "tsyringe";
import { Kavenegar, SendResponse } from "kavenegar-client";
import { sanitizeContent } from "../utils/strings";

// SMS Template Constants
const SMS_TEMPLATES = {
    SIGNUP_VERIFICATION: "signupVerification",
    SELLER_NOTIFICATION: "sellerNotification",
    SUBSCRIPTION_EXPIRY: "subscriptionExpiry",
    SUBSCRIPTION_EXPIRED: "subscriptionExpired",
    BOT_MESSAGE_NEAR_LIMIT: "botMessageNearLimit",
    BOT_MESSAGE_LIMIT_REACHED: "botMessageLimitReached",
} as const;

@singleton()
export class KavenegarService {
    private _client: Kavenegar;

    constructor() {
        this._client = new Kavenegar(process.env.KAVENEGAR_API_KEY);
    }

    async sendOtp(phone: string, otp: string): Promise<SendResponse> {
        // Apply sanitization to ensure the OTP doesn't contain invalid characters
        const sanitizedOtp = sanitizeContent(otp);

        console.log("[KavenegarService] Sending OTP with sanitized value:", {
            original: otp,
            sanitized: sanitizedOtp,
            phone,
            template: SMS_TEMPLATES.SIGNUP_VERIFICATION,
        });

        try {
            const response = await this._client.verifyLookup({
                type: "sms",
                receptor: phone,
                token: sanitizedOtp,
                template: SMS_TEMPLATES.SIGNUP_VERIFICATION,
            });

            console.log("[KavenegarService] Successfully sent OTP SMS:", {
                phone,
                otp: sanitizedOtp,
                response,
            });

            return response;
        } catch (error: unknown) {
            console.error("[KavenegarService] Failed to send OTP SMS:", {
                phone,
                otp: sanitizedOtp,
                error: error instanceof Error ? error.message : "Unknown error",
                stack: error instanceof Error ? error.stack : undefined,
            });
            throw error;
        }
    }

    async sendSellerNotification(
        phone: string,
        orderId: string,
        userName: string,
    ): Promise<SendResponse> {
        // Apply sanitization directly in the service to ensure it's always done
        const sanitizedOrderId = sanitizeContent(orderId);
        const sanitizedUserName = sanitizeContent(userName);

        console.log(
            "[KavenegarService] Sending seller notification SMS with sanitized orderId:",
            {
                original: orderId,
                sanitizedOrderId,
                sanitizedUserName,
                phone,
                template: SMS_TEMPLATES.SELLER_NOTIFICATION,
            },
        );

        try {
            const response = await this._client.verifyLookup({
                type: "sms",
                receptor: phone,
                token: sanitizedOrderId,
                token2: sanitizedUserName,
                template: SMS_TEMPLATES.SELLER_NOTIFICATION,
            });

            console.log(
                "[KavenegarService] Successfully sent seller notification SMS:",
                {
                    phone,
                    orderId: sanitizedOrderId,
                    response,
                },
            );

            return response;
        } catch (error: unknown) {
            console.error(
                "[KavenegarService] Failed to send seller notification SMS:",
                {
                    phone,
                    orderId: sanitizedOrderId,
                    error:
                        error instanceof Error
                            ? error.message
                            : "Unknown error",
                    stack: error instanceof Error ? error.stack : undefined,
                },
            );
            throw error;
        }
    }

    async sendSubscriptionExpiryNotification(
        phone: string,
        userName: string,
        planName: string,
        remainingDays: string,
    ): Promise<SendResponse> {
        // Apply sanitization to ensure the content doesn't contain invalid characters
        const sanitizedUserName = sanitizeContent(userName);
        const sanitizedPlanName = sanitizeContent(planName);
        const sanitizedRemainingDays = sanitizeContent(remainingDays);

        console.log(
            "[KavenegarService] Sending subscription expiry notification SMS:",
            {
                phone,
                userName: sanitizedUserName,
                planName: sanitizedPlanName,
                remainingDays: sanitizedRemainingDays,
                template: SMS_TEMPLATES.SUBSCRIPTION_EXPIRY,
            },
        );

        try {
            const response = await this._client.verifyLookup({
                type: "sms",
                receptor: phone,
                token: sanitizedUserName,
                token2: sanitizedPlanName,
                token3: sanitizedRemainingDays,
                template: SMS_TEMPLATES.SUBSCRIPTION_EXPIRY,
            });

            console.log(
                "[KavenegarService] Successfully sent subscription expiry notification SMS:",
                {
                    phone,
                    userName: sanitizedUserName,
                    planName: sanitizedPlanName,
                    remainingDays: sanitizedRemainingDays,
                    response,
                },
            );

            return response;
        } catch (error: unknown) {
            console.error(
                "[KavenegarService] Failed to send subscription expiry notification SMS:",
                {
                    phone,
                    userName: sanitizedUserName,
                    planName: sanitizedPlanName,
                    remainingDays: sanitizedRemainingDays,
                    error:
                        error instanceof Error
                            ? error.message
                            : "Unknown error",
                    stack: error instanceof Error ? error.stack : undefined,
                },
            );
            throw error;
        }
    }

    async sendSubscriptionExpiredNotification(
        phone: string,
        userName: string,
        planName: string,
    ): Promise<SendResponse> {
        // Apply sanitization to ensure the content doesn't contain invalid characters
        const sanitizedUserName = sanitizeContent(userName);
        const sanitizedPlanName = sanitizeContent(planName);

        console.log(
            "[KavenegarService] Sending subscription expired notification SMS:",
            {
                phone,
                userName: sanitizedUserName,
                planName: sanitizedPlanName,
                template: SMS_TEMPLATES.SUBSCRIPTION_EXPIRED,
            },
        );

        try {
            const response = await this._client.verifyLookup({
                type: "sms",
                receptor: phone,
                token: sanitizedUserName,
                token2: sanitizedPlanName,
                template: SMS_TEMPLATES.SUBSCRIPTION_EXPIRED,
            });

            console.log(
                "[KavenegarService] Successfully sent subscription expired notification SMS:",
                {
                    phone,
                    userName: sanitizedUserName,
                    planName: sanitizedPlanName,
                    response,
                },
            );

            return response;
        } catch (error: unknown) {
            console.error(
                "[KavenegarService] Failed to send subscription expired notification SMS:",
                {
                    phone,
                    userName: sanitizedUserName,
                    planName: sanitizedPlanName,
                    error:
                        error instanceof Error
                            ? error.message
                            : "Unknown error",
                    stack: error instanceof Error ? error.stack : undefined,
                },
            );
            throw error;
        }
    }

    async sendBotMessageNearLimitNotification(
        phone: string,
        remainingMessages: string,
    ): Promise<SendResponse> {
        // Apply sanitization to ensure the content doesn't contain invalid characters
        const sanitizedRemainingMessages = sanitizeContent(remainingMessages);

        console.log(
            "[KavenegarService] Sending bot message near limit notification SMS:",
            {
                phone,
                remainingMessages: sanitizedRemainingMessages,
                template: SMS_TEMPLATES.BOT_MESSAGE_NEAR_LIMIT,
            },
        );

        try {
            const response = await this._client.verifyLookup({
                type: "sms",
                receptor: phone,
                token: sanitizedRemainingMessages,
                template: SMS_TEMPLATES.BOT_MESSAGE_NEAR_LIMIT,
            });

            console.log(
                "[KavenegarService] Successfully sent bot message near limit notification SMS:",
                {
                    phone,
                    remainingMessages: sanitizedRemainingMessages,
                    response,
                },
            );

            return response;
        } catch (error: unknown) {
            console.error(
                "[KavenegarService] Failed to send bot message near limit notification SMS:",
                {
                    phone,
                    remainingMessages: sanitizedRemainingMessages,
                    error:
                        error instanceof Error
                            ? error.message
                            : "Unknown error",
                    stack: error instanceof Error ? error.stack : undefined,
                },
            );
            throw error;
        }
    }

    async sendBotMessageLimitReachedNotification(
        phone: string,
    ): Promise<SendResponse> {
        console.log(
            "[KavenegarService] Sending bot message limit reached notification SMS:",
            {
                phone,
                template: SMS_TEMPLATES.BOT_MESSAGE_LIMIT_REACHED,
            },
        );

        try {
            const response = await this._client.verifyLookup({
                type: "sms",
                receptor: phone,
                template: SMS_TEMPLATES.BOT_MESSAGE_LIMIT_REACHED,
                token: phone, // Assuming the phone number is used as a token for this template
            });

            console.log(
                "[KavenegarService] Successfully sent bot message limit reached notification SMS:",
                {
                    phone,
                    response,
                },
            );

            return response;
        } catch (error: unknown) {
            console.error(
                "[KavenegarService] Failed to send bot message limit reached notification SMS:",
                {
                    phone,
                    error:
                        error instanceof Error
                            ? error.message
                            : "Unknown error",
                    stack: error instanceof Error ? error.stack : undefined,
                },
            );
            throw error;
        }
    }
}
