import express from "express";
import { container } from "tsyringe";
import SubscriptionSchedulerController from "./subscription-scheduler.controller";
import { JWT } from "../../../common/lib/middlewares";

const router = express.Router();

const controller = container.resolve(SubscriptionSchedulerController);

router.post(
    "/trigger/expired-check",
    JW<PERSON>,
    controller.triggerExpiredSubscriptionsCheck,
);
router.post(
    "/trigger/expiry-notification",
    JW<PERSON>,
    controller.triggerExpiryNotificationCheck,
);

export default router;
