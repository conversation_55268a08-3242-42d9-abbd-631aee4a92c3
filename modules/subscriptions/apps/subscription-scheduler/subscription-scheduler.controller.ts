import { injectable } from "tsyringe";
import { Request, Response } from "express";
import SubscriptionSchedulerService from "./subscription-scheduler.service";
import { OpenAPI } from "../../../common/lib/decorators";
import { TriggerSchedulerResponseSchema } from "./responses";

@injectable()
export default class SubscriptionSchedulerController {
    constructor(private _service: SubscriptionSchedulerService) {}

    @OpenAPI(
        "subscription-scheduler",
        "/trigger/expired-check",
        "post",
        undefined,
        undefined,
        TriggerSchedulerResponseSchema,
        "bearerAuth",
    )
    triggerExpiredSubscriptionsCheck = async (req: Request, res: Response) => {
        await this._service.triggerExpiredSubscriptionsCheck();
        res.success({
            message: "Expired subscriptions check triggered successfully",
        });
    };

    @OpenAPI(
        "subscription-scheduler",
        "/trigger/expiry-notification",
        "post",
        undefined,
        undefined,
        TriggerSchedulerResponseSchema,
        "bearerAuth",
    )
    triggerExpiryNotificationCheck = async (req: Request, res: Response) => {
        await this._service.triggerExpiryNotificationCheck();
        res.success({
            message: "Expiry notification check triggered successfully",
        });
    };
}
