import { inject, injectable, registry } from "tsyringe";
import { RedisConnection } from "../../../common/lib/redis";
import { BullMQModule, createJobProcessor } from "../../../common/lib/bullmq";
import { Job, Processor } from "bullmq";
import UserSubscriptionRepo from "../user-subscriptions/user-subscription.repo";
import { KavenegarService } from "../../../common/lib/sms";
import Logger from "../../../common/lib/metrics/logger";
import { UserLanguageService } from "../../../common/lib/services/user-language.service";
import UsersService from "../../../users/apps/users/users.service";
import { registries } from "./registries/index";
import {
    SUBSCRIPTION_REDIS_KEYS,
    SUBSCRIPTION_TTL,
} from "../../shared/redis-keys.constants";

@registry(registries)
@injectable()
export default class SubscriptionSchedulerService {
    private _expiredSubscriptionsQueue: BullMQModule<unknown>;
    private _expiryNotificationQueue: BullMQModule<unknown>;
    private _expiredSubscriptionsJobName: string =
        "expired-subscriptions-check";
    private _expiryNotificationJobName: string = "expiry-notification-check";
    private _redisConnection: RedisConnection;

    constructor(
        private _userSubscriptionRepo: UserSubscriptionRepo,
        private _smsService: KavenegarService,
        private _userLanguageService: UserLanguageService,
        private _usersService: UsersService,
        @inject("queue")
        createQueueModule: <T>(
            queueName: string,
            processor: Processor<T>,
            redisConnection: RedisConnection,
        ) => BullMQModule<T>,
        @inject("redis")
        createRedisConnection: () => RedisConnection,
    ) {
        this._redisConnection = createRedisConnection();

        // Create processor for expired subscriptions check
        const expiredSubscriptionsProcessor: Processor = createJobProcessor(
            this.checkExpiredSubscriptions.bind(this),
        );
        this._expiredSubscriptionsQueue = createQueueModule(
            this._expiredSubscriptionsJobName,
            expiredSubscriptionsProcessor,
            this._redisConnection,
        );

        // Create processor for expiry notifications
        const expiryNotificationProcessor: Processor = createJobProcessor(
            this.checkExpiryNotifications.bind(this),
        );
        this._expiryNotificationQueue = createQueueModule(
            this._expiryNotificationJobName,
            expiryNotificationProcessor,
            this._redisConnection,
        );
    }

    /**
     * Check for expired subscriptions and update their status to EXPIRED
     */
    private async checkExpiredSubscriptions(): Promise<void> {
        try {
            Logger.info("Starting expired subscriptions check...");

            // Find all subscriptions that are EXECUTED but have passed their end date
            const expiredSubscriptions =
                await this._userSubscriptionRepo.findExpiredSubscriptions();

            if (expiredSubscriptions.length === 0) {
                Logger.info("No expired subscriptions found");
                return;
            }

            Logger.info(
                `Found ${expiredSubscriptions.length} expired subscriptions`,
            );

            // Update all expired subscriptions to EXPIRED status and send notifications
            for (const subscription of expiredSubscriptions) {
                await this._userSubscriptionRepo.updateById(subscription.id, {
                    status: "EXPIRED" as any,
                });

                // Send SMS notification to user about expired subscription
                try {
                    await this.sendExpiredSubscriptionSMS(subscription);
                } catch (error) {
                    Logger.error(
                        `Failed to send expired subscription SMS for subscription ${subscription.id}:`,
                        {
                            error:
                                error instanceof Error
                                    ? error.message
                                    : String(error),
                            subscriptionId: subscription.id,
                            userId: subscription.userId,
                        },
                    );
                }

                // Clean up Redis data for the expired subscription
                await this.cleanupExpiredSubscriptionData(subscription);

                Logger.info(
                    `Updated subscription ${subscription.id} to EXPIRED status`,
                    {
                        subscriptionId: subscription.id,
                        userId: subscription.userId,
                        endDate: subscription.endDate,
                    },
                );
            }

            Logger.info(
                `Successfully updated ${expiredSubscriptions.length} expired subscriptions`,
            );
        } catch (error) {
            Logger.error("Error checking expired subscriptions:", {
                error: error instanceof Error ? error.message : String(error),
            });
        }
    }

    /**
     * Check for subscriptions expiring in 3 days and send SMS notifications
     */
    private async checkExpiryNotifications(): Promise<void> {
        try {
            Logger.info("Starting expiry notifications check...");

            // Find all subscriptions that expire in exactly 3 days
            const subscriptionsNearExpiry =
                await this._userSubscriptionRepo.findSubscriptionsExpiringInDays(
                    3,
                );

            if (subscriptionsNearExpiry.length === 0) {
                Logger.info("No subscriptions expiring in 3 days found");
                return;
            }

            Logger.info(
                `Found ${subscriptionsNearExpiry.length} subscriptions expiring in 3 days`,
                {
                    subscriptionsNearExpiry,
                },
            );

            const redis = this._redisConnection.getConnection();

            // Send SMS notification for each subscription (only once per subscription)
            for (const subscription of subscriptionsNearExpiry) {
                const notificationKey =
                    SUBSCRIPTION_REDIS_KEYS.EXPIRY_NOTIFICATION(
                        subscription.id,
                    );

                // Check if we already sent notification for this subscription
                const hasNotified = await redis.get(notificationKey);
                if (hasNotified) {
                    Logger.info(
                        `Notification already sent for subscription ${subscription.id}`,
                    );
                    continue;
                }

                try {
                    Logger.info(`User:`, {
                        userId: subscription.userId,
                        userPhone: subscription.user,
                    });
                    // Send SMS notification
                    await this.sendExpiryNotificationSMS(subscription);

                    // Mark as notified using shared TTL constant
                    await redis.setex(
                        notificationKey,
                        SUBSCRIPTION_TTL.EXPIRY_NOTIFICATION,
                        "1",
                    );

                    Logger.info(
                        `Sent expiry notification for subscription ${subscription.id}`,
                        {
                            subscriptionId: subscription.id,
                            userId: subscription.userId,
                            user: subscription.user,
                            endDate: subscription.endDate,
                        },
                    );
                } catch (error) {
                    Logger.error(
                        `Failed to send expiry notification for subscription ${subscription.id}:`,
                        {
                            error:
                                error instanceof Error
                                    ? error.message
                                    : String(error),
                        },
                    );
                }
            }

            Logger.info(`Completed expiry notifications check`);
        } catch (error) {
            Logger.error("Error checking expiry notifications:", {
                error: error instanceof Error ? error.message : String(error),
            });
        }
    }

    /**
     * Send SMS notification to user about subscription expiring in 3 days
     */
    private async sendExpiryNotificationSMS(subscription: any): Promise<void> {
        const { userId, plan, endDate } = subscription;

        // Get user data separately using the UsersService
        const user = await this._usersService.getUserById(userId);
        if (!user.phone) {
            Logger.warn(
                `User ${user.id} has no phone number for expiry notification`,
            );
            return;
        }

        // Calculate remaining days
        const now = new Date();
        const subscriptionEndDate = new Date(endDate);
        const timeDifference = subscriptionEndDate.getTime() - now.getTime();
        const remainingDays = Math.ceil(timeDifference / (1000 * 3600 * 24));

        // Get user's language preference from database
        const userLanguage = await this._userLanguageService.getUserLanguage(
            user.id,
        );
        const isEnglish = userLanguage === "en";

        let message: string;
        if (isEnglish) {
            message = `Dear User, your ${plan.name} subscription will expire in ${remainingDays} days. Please renew to continue using our services.`;
        } else {
            message = `کاربر گرامی , اشتراک ${plan.name} شما ${remainingDays} روز دیگر به پایان می‌رسد. لطفاً برای ادامه استفاده از خدمات، اشتراک خود را تمدید کنید.`;
        }

        // Use a custom SMS template for subscription expiry (you'll need to create this template in Kavenegar)
        try {
            // Use the template-based SMS method with remaining days
            await this._smsService.sendSubscriptionExpiryNotification(
                user.phone,
                "کاربر",
                plan.name,
                remainingDays.toString(),
            );
        } catch (error) {
            Logger.error(`Failed to send SMS to ${user.phone}:`, {
                error: error instanceof Error ? error.message : String(error),
            });
            throw error;
        }
    }

    /**
     * Send SMS notification to user about subscription expiration
     */
    private async sendExpiredSubscriptionSMS(subscription: any): Promise<void> {
        const { userId, plan } = subscription;

        // Get user data separately using the UsersService
        const user = await this._usersService.getUserById(userId);

        if (!user.phone) {
            Logger.warn(
                `User ${user.id} has no phone number for expired subscription notification`,
            );
            return;
        }

        // Get user's language preference from database
        const userLanguage = await this._userLanguageService.getUserLanguage(
            user.id,
        );
        const isEnglish = userLanguage === "en";

        let message: string;
        if (isEnglish) {
            message = `Dear User, your ${plan.name} subscription has expired. Please renew to continue using our services.`;
        } else {
            message = `کاربر گرامی , اشتراک ${plan.name} شما منقضی شده است. لطفاً برای ادامه استفاده از خدمات، اشتراک خود را تمدید کنید.`;
        }

        // Use a custom SMS template for subscription expiration (you'll need to create this template in Kavenegar)
        try {
            // Use the template-based SMS method for expired subscription
            await this._smsService.sendSubscriptionExpiredNotification(
                user.phone,
                "کاربر",
                plan.name,
            );
        } catch (error) {
            Logger.error(`Failed to send SMS to ${user.phone}:`, {
                error: error instanceof Error ? error.message : String(error),
            });
            throw error;
        }
    }

    /**
     * Schedule the expired subscriptions check job to run every hour
     */
    async scheduleExpiredSubscriptionsCheck(): Promise<void> {
        try {
            await this._expiredSubscriptionsQueue.addRepeatableJob(
                this._expiredSubscriptionsJobName,
                {},
                {
                    every: 60 * 60 * 1000, // Every hour
                },
            );
            Logger.info(
                "Scheduled expired subscriptions check job to run every hour",
            );
        } catch (error) {
            Logger.error(
                "Failed to schedule expired subscriptions check job:",
                {
                    error:
                        error instanceof Error ? error.message : String(error),
                },
            );
        }
    }

    /**
     * Schedule the expiry notification check job to run every 6 hours
     */
    async scheduleExpiryNotificationCheck(): Promise<void> {
        try {
            await this._expiryNotificationQueue.addRepeatableJob(
                this._expiryNotificationJobName,
                {},
                {
                    every: 6 * 60 * 60 * 1000, // Every 6 hours
                },
            );
            Logger.info(
                "Scheduled expiry notification check job to run every 6 hours",
            );
        } catch (error) {
            Logger.error("Failed to schedule expiry notification check job:", {
                error: error instanceof Error ? error.message : String(error),
            });
        }
    }

    /**
     * Initialize all scheduled jobs
     */
    async initializeScheduledJobs(): Promise<void> {
        await this.scheduleExpiredSubscriptionsCheck();
        await this.scheduleExpiryNotificationCheck();
        Logger.info("Subscription scheduler jobs initialized successfully");
    }

    /**
     * Manually trigger expired subscriptions check (for testing)
     */
    async triggerExpiredSubscriptionsCheck(): Promise<void> {
        await this._expiredSubscriptionsQueue.addJob(
            this._expiredSubscriptionsJobName,
            {},
        );
        Logger.info("Manually triggered expired subscriptions check");
    }

    /**
     * Manually trigger expiry notification check (for testing)
     */
    async triggerExpiryNotificationCheck(): Promise<void> {
        await this._expiryNotificationQueue.addJob(
            this._expiryNotificationJobName,
            {},
        );
        Logger.info("Manually triggered expiry notification check");
    }

    /**
     * Clean up all Redis keys for an expired subscription
     * This includes usage tracking keys and notification keys
     * @param subscription - The expired subscription to clean up
     */
    private async cleanupExpiredSubscriptionData(
        subscription: any,
    ): Promise<void> {
        try {
            const redis = this._redisConnection.getConnection();
            const pattern = SUBSCRIPTION_REDIS_KEYS.SUBSCRIPTION_PATTERN(
                subscription.id,
            );

            // Get all keys for this subscription
            const keys = await redis.keys(pattern);

            if (keys.length > 0) {
                // Delete all keys for this subscription
                await redis.del(...keys);
                Logger.info(
                    `Cleaned up ${keys.length} Redis keys for expired subscription ${subscription.id}`,
                    {
                        subscriptionId: subscription.id,
                        userId: subscription.userId,
                        keysDeleted: keys.length,
                        keyPattern: pattern,
                    },
                );
            } else {
                Logger.info(
                    `No Redis keys found to cleanup for subscription ${subscription.id}`,
                    {
                        subscriptionId: subscription.id,
                        userId: subscription.userId,
                        keyPattern: pattern,
                    },
                );
            }
        } catch (error) {
            Logger.error(
                `Failed to cleanup Redis data for subscription ${subscription.id}:`,
                {
                    error:
                        error instanceof Error ? error.message : String(error),
                    subscriptionId: subscription.id,
                },
            );
        }
    }

    /**
     * Manually cleanup expired subscription data (for maintenance)
     * This method can be called manually to clean up Redis data for expired subscriptions
     * @param subscriptionId - Optional specific subscription ID to clean up
     */
    async manualCleanupExpiredSubscriptions(
        subscriptionId?: number,
    ): Promise<void> {
        try {
            Logger.info(
                "Starting manual cleanup of expired subscription data...",
            );

            let subscriptionsToCleanup: any[];

            if (subscriptionId) {
                // Clean up specific subscription
                const subscription =
                    await this._userSubscriptionRepo.findById(subscriptionId);
                if (!subscription) {
                    Logger.warn(
                        `Subscription ${subscriptionId} not found for cleanup`,
                    );
                    return;
                }
                subscriptionsToCleanup = [subscription];
            } else {
                // Clean up all expired subscriptions
                const expiredSubscriptions =
                    await this._userSubscriptionRepo.findByConditions({
                        status: "EXPIRED",
                    });
                subscriptionsToCleanup = expiredSubscriptions;
            }

            if (subscriptionsToCleanup.length === 0) {
                Logger.info("No expired subscriptions found for cleanup");
                return;
            }

            Logger.info(
                `Found ${subscriptionsToCleanup.length} expired subscriptions to cleanup`,
            );

            // Clean up Redis data for each expired subscription
            for (const subscription of subscriptionsToCleanup) {
                await this.cleanupExpiredSubscriptionData(subscription);
            }

            Logger.info(
                `Completed manual cleanup for ${subscriptionsToCleanup.length} expired subscriptions`,
            );
        } catch (error) {
            Logger.error(
                "Error during manual cleanup of expired subscriptions:",
                {
                    error:
                        error instanceof Error ? error.message : String(error),
                },
            );
        }
    }

    /**
     * Close all queues
     */
    async closeJob(): Promise<void> {
        await this._expiredSubscriptionsQueue.close();
        await this._expiryNotificationQueue.close();
        Logger.info("Subscription scheduler queues closed");
    }
}
