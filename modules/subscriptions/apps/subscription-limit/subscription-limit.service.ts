import { delay, inject, injectable } from "tsyringe";
import { RedisConnection } from "../../../common/lib/redis";
import UserSubscriptionService from "../user-subscriptions/user-subscription.service";
import SubscriptionPlanService from "../subscriptions-plan/subscription-plan.service";
import InventoryService from "../../../inventories/apps/inventories/inventories.service";
import {
    SUBSCRIPTION_REDIS_KEYS,
    SUBSCRIPTION_TTL,
} from "../../shared/redis-keys.constants";
import { CurrentSubscriptionRow } from "../user-subscriptions/types";
import { KavenegarService } from "../../../common/lib/sms/module";
import { UserLanguageService } from "../../../common/lib/services/user-language.service";
import UsersService from "../../../users/apps/users/users.service";
import { AppLanguage } from "../../../common/base/types/typing";
import Logger from "../../../common/lib/metrics/logger";

// Feature limit constants
export enum FEATURES {
    CHATBOT_MESSAGE = "CHATBOT_MESSAGE",
    INVENTORY_PRODUCT = "INVENTORY_PRODUCT",
}

export interface BotMessageLimitResult {
    isAllowed: boolean;
    shouldNotifyNearLimit: boolean; // Should send 80% warning notification
    shouldNotifyLimitReached: boolean; // Should send limit reached notification
    currentUsage: number;
    limit: number;
    remainingUsage: number;
}

export interface FeatureLimitResult {
    isAllowed: boolean;
    currentUsage: number;
    limit: number;
    remainingUsage: number;
    feature: FEATURES;
}

//@registry(registries)
@injectable()
export default class SubscriptionLimitService {
    private _redisConnection: RedisConnection;

    constructor(
        @inject(delay(() => UserSubscriptionService))
        private _userSubscriptionService: UserSubscriptionService,
        @inject(delay(() => SubscriptionPlanService))
        private _subscriptionPlanService: SubscriptionPlanService,
        @inject(delay(() => InventoryService))
        private _inventoryService: InventoryService,
        @inject(delay(() => UsersService))
        private _usersService: UsersService,
        private _kavenegarService: KavenegarService,
        private _userLanguageService: UserLanguageService,
        @inject("redis")
        createRedisConnection: () => RedisConnection,
    ) {
        this._redisConnection = createRedisConnection();
    }

    /**
     * Get active subscription for user (status = EXECUTED, current date between startDate and endDate)
     * @param userId - The user ID
     * @returns Promise<ActiveSubscription | null>
     */
    private async getActiveSubscription(
        userId: number,
    ): Promise<CurrentSubscriptionRow | null> {
        const subscription =
            await this._userSubscriptionService.getCurrentSubscriptionOfUser(
                userId,
            );

        if (!subscription) {
            return null;
        }

        // Check if subscription is actually active (EXECUTED status and within date range)
        const now = new Date();
        const startDate = new Date(subscription.startDate);
        const endDate = new Date(subscription.endDate);

        if (
            subscription.status === "EXECUTED" &&
            now >= startDate &&
            now <= endDate
        ) {
            return subscription;
        }

        return null;
    }

    /**
     * Calculate current cycle based on subscription start date, current date, and duration
     * @param startDate - Subscription start date
     * @param currentDate - Current date
     * @param duration - Total duration in months
     * @returns Current cycle number (1-based) and cycle dates
     */
    private getCurrentCycle(
        startDate: Date,
        currentDate: Date,
        duration: number,
    ): {
        cycle: number;
        cycleStartDate: Date;
        cycleEndDate: Date;
    } {
        const start = new Date(startDate);
        const current = new Date(currentDate);

        // Calculate which month cycle we're in (1-based)
        let cycle = 1;
        let cycleStartDate = new Date(start);
        let cycleEndDate = new Date(start);
        cycleEndDate.setMonth(cycleEndDate.getMonth() + 1);

        // Find the current cycle
        while (cycle <= duration && current >= cycleEndDate) {
            cycle++;
            cycleStartDate = new Date(cycleEndDate);
            cycleEndDate = new Date(cycleStartDate);
            cycleEndDate.setMonth(cycleEndDate.getMonth() + 1);
        }

        // If we're beyond the last cycle, return the last cycle
        if (cycle > duration) {
            cycle = duration;
            cycleStartDate = new Date(start);
            cycleStartDate.setMonth(cycleStartDate.getMonth() + (duration - 1));
            cycleEndDate = new Date(start);
            cycleEndDate.setMonth(cycleEndDate.getMonth() + duration);
        }

        return {
            cycle,
            cycleStartDate,
            cycleEndDate,
        };
    }

    /**
     * Check if user can send more bot messages and update usage
     * @param userId - The user ID to check
     * @returns Promise<BotMessageLimitResult>
     */
    async checkBotMessageLimit(userId: number): Promise<BotMessageLimitResult> {
        try {
            // Use the existing addFeatureLimit method for CHATBOT_MESSAGE
            const result = await this.addFeatureLimit(
                userId,
                FEATURES.CHATBOT_MESSAGE,
            );

            if (!result.isAllowed) {
                // Get active subscription for notification keys
                const subscription = await this.getActiveSubscription(userId);

                if (subscription) {
                    // Get current cycle
                    const { cycle } = this.getCurrentCycle(
                        new Date(subscription.startDate),
                        new Date(),
                        subscription.plan.duration,
                    );

                    // Check if we should notify about limit reached (only once per cycle)
                    const shouldNotifyLimitReached =
                        await this.checkAndSetNotificationFlag(
                            subscription.id,
                            cycle,
                            "bot_message_limit_reached_notif",
                        );

                    return {
                        isAllowed: false,
                        shouldNotifyNearLimit: false,
                        shouldNotifyLimitReached,
                        currentUsage: result.currentUsage,
                        limit: result.limit,
                        remainingUsage: 0,
                    };
                }

                return {
                    isAllowed: false,
                    shouldNotifyNearLimit: false,
                    shouldNotifyLimitReached: false,
                    currentUsage: result.currentUsage,
                    limit: result.limit,
                    remainingUsage: 0,
                };
            }

            // Check if near limit (80%) and should notify
            const nearLimitThreshold = Math.floor(result.limit * 0.8);
            const isNearLimit = result.currentUsage >= nearLimitThreshold;

            let shouldNotifyNearLimit = false;

            if (isNearLimit) {
                const subscription = await this.getActiveSubscription(userId);
                if (subscription) {
                    // Get current cycle
                    const { cycle } = this.getCurrentCycle(
                        new Date(subscription.startDate),
                        new Date(),
                        subscription.plan.duration,
                    );

                    shouldNotifyNearLimit =
                        await this.checkAndSetNotificationFlag(
                            subscription.id,
                            cycle,
                            "bot_message_near_limit_notif",
                        );
                }
            }

            return {
                isAllowed: true,
                shouldNotifyNearLimit,
                shouldNotifyLimitReached: false,
                currentUsage: result.currentUsage,
                limit: result.limit,
                remainingUsage: result.remainingUsage,
            };
        } catch (error) {
            console.error("Error checking bot message limit:", error);
            // On error, don't allow to be safe
            return {
                isAllowed: false,
                shouldNotifyNearLimit: false,
                shouldNotifyLimitReached: false,
                currentUsage: 0,
                limit: 0,
                remainingUsage: 0,
            };
        }
    }

    /**
     * Add usage for a specific feature (increment by 1)
     * @param userId - The user ID
     * @param feature - The feature to increment usage for
     * @returns Promise<FeatureLimitResult>
     */
    async addFeatureLimit(
        userId: number,
        feature: FEATURES,
    ): Promise<FeatureLimitResult> {
        try {
            // Get active subscription
            const subscription = await this.getActiveSubscription(userId);

            if (!subscription) {
                return {
                    isAllowed: false,
                    currentUsage: 0,
                    limit: 0,
                    remainingUsage: 0,
                    feature,
                };
            }

            // Get full subscription plan details
            const subscriptionPlan =
                await this._subscriptionPlanService.getSubscriptionPlan(
                    subscription.plan.id,
                );

            if (!subscriptionPlan) {
                return {
                    isAllowed: false,
                    currentUsage: 0,
                    limit: 0,
                    remainingUsage: 0,
                    feature,
                };
            }

            const featureLimit = this.getFeatureLimitFromPlan(
                subscriptionPlan,
                feature,
            );

            // If no limit is set (unlimited), allow
            if (!featureLimit || featureLimit <= 0) {
                return {
                    isAllowed: true,
                    currentUsage: 0,
                    limit: 0,
                    remainingUsage: 0,
                    feature,
                };
            }

            // Get current cycle information
            const now = new Date();
            const { cycle, cycleEndDate } = this.getCurrentCycle(
                new Date(subscription.startDate),
                now,
                subscription.plan.duration,
            );

            // Generate Redis key for this subscription, cycle, and feature
            const usageKey = SUBSCRIPTION_REDIS_KEYS.USAGE(
                subscription.id,
                feature,
                cycle,
            );

            // Calculate TTL to current cycle end date (in seconds)
            const ttlSeconds = Math.floor(
                (cycleEndDate.getTime() - now.getTime()) / 1000,
            );

            // If TTL is negative or zero, cycle has expired
            if (ttlSeconds <= 0) {
                return {
                    isAllowed: false,
                    currentUsage: 0,
                    limit: featureLimit,
                    remainingUsage: 0,
                    feature,
                };
            }

            // Get current usage for this cycle
            const redis = this._redisConnection.getConnection();
            const currentUsageStr = await redis.get(usageKey);
            const currentUsage = currentUsageStr
                ? parseInt(currentUsageStr, 10)
                : 0;

            // Check if limit is reached
            if (currentUsage >= featureLimit) {
                return {
                    isAllowed: false,
                    currentUsage,
                    limit: featureLimit,
                    remainingUsage: 0,
                    feature,
                };
            }

            // Increment usage
            const newUsage = currentUsage + 1;

            // Set usage with TTL to current cycle end date
            await redis.setex(usageKey, ttlSeconds, newUsage.toString());

            return {
                isAllowed: true,
                currentUsage: newUsage,
                limit: featureLimit,
                remainingUsage: featureLimit - newUsage,
                feature,
            };
        } catch (error) {
            console.error(`Error adding feature limit for ${feature}:`, error);
            // On error, don't allow to be safe
            return {
                isAllowed: false,
                currentUsage: 0,
                limit: 0,
                remainingUsage: 0,
                feature,
            };
        }
    }

    /**
     * Remove usage for a specific feature (decrement by 1)
     * @param userId - The user ID
     * @param feature - The feature to decrement usage for
     * @returns Promise<FeatureLimitResult>
     */
    async removeFeatureLimit(
        userId: number,
        feature: FEATURES,
    ): Promise<FeatureLimitResult> {
        try {
            // Get active subscription
            const subscription = await this.getActiveSubscription(userId);

            if (!subscription) {
                return {
                    isAllowed: true,
                    currentUsage: 0,
                    limit: 0,
                    remainingUsage: 0,
                    feature,
                };
            }

            // Get full subscription plan details
            const subscriptionPlan =
                await this._subscriptionPlanService.getSubscriptionPlan(
                    subscription.plan.id,
                );

            if (!subscriptionPlan) {
                return {
                    isAllowed: true,
                    currentUsage: 0,
                    limit: 0,
                    remainingUsage: 0,
                    feature,
                };
            }

            const featureLimit = this.getFeatureLimitFromPlan(
                subscriptionPlan,
                feature,
            );

            // Get current cycle information
            const now = new Date();
            const { cycle, cycleEndDate } = this.getCurrentCycle(
                new Date(subscription.startDate),
                now,
                subscription.plan.duration,
            );

            // Generate Redis key for this subscription, cycle, and feature
            const usageKey = SUBSCRIPTION_REDIS_KEYS.USAGE(
                subscription.id,
                feature,
                cycle,
            );

            // Calculate TTL to current cycle end date (in seconds)
            const ttlSeconds = Math.floor(
                (cycleEndDate.getTime() - now.getTime()) / 1000,
            );

            // Get current usage
            const redis = this._redisConnection.getConnection();
            const currentUsageStr = await redis.get(usageKey);
            const currentUsage = currentUsageStr
                ? parseInt(currentUsageStr, 10)
                : 0;

            // Decrement usage (don't go below 0)
            const newUsage = Math.max(0, currentUsage - 1);

            // Set usage with TTL to current cycle end date
            if (newUsage > 0 && ttlSeconds > 0) {
                await redis.setex(usageKey, ttlSeconds, newUsage.toString());
            } else {
                await redis.del(usageKey);
            }

            return {
                isAllowed: true,
                currentUsage: newUsage,
                limit: featureLimit || 0,
                remainingUsage: featureLimit ? featureLimit - newUsage : 0,
                feature,
            };
        } catch (error) {
            console.error(
                `Error removing feature limit for ${feature}:`,
                error,
            );
            return {
                isAllowed: true,
                currentUsage: 0,
                limit: 0,
                remainingUsage: 0,
                feature,
            };
        }
    }

    /**
     * Check current usage for a specific feature without incrementing
     * @param userId - The user ID
     * @param feature - The feature to check usage for
     * @returns Promise<FeatureLimitResult>
     */
    async checkFeatureLimit(
        userId: number,
        feature: FEATURES,
    ): Promise<FeatureLimitResult> {
        try {
            // Get active subscription
            const subscription = await this.getActiveSubscription(userId);

            if (!subscription) {
                return {
                    isAllowed: false,
                    currentUsage: 0,
                    limit: 0,
                    remainingUsage: 0,
                    feature,
                };
            }

            // Get full subscription plan details
            const subscriptionPlan =
                await this._subscriptionPlanService.getSubscriptionPlan(
                    subscription.plan.id,
                );

            if (!subscriptionPlan) {
                return {
                    isAllowed: false,
                    currentUsage: 0,
                    limit: 0,
                    remainingUsage: 0,
                    feature,
                };
            }

            const featureLimit = this.getFeatureLimitFromPlan(
                subscriptionPlan,
                feature,
            );

            // If no limit is set (unlimited), allow
            if (!featureLimit || featureLimit <= 0) {
                return {
                    isAllowed: true,
                    currentUsage: 0,
                    limit: 0,
                    remainingUsage: 0,
                    feature,
                };
            }

            // Get current cycle information
            const now = new Date();
            const { cycle } = this.getCurrentCycle(
                new Date(subscription.startDate),
                now,
                subscription.plan.duration,
            );

            // Generate Redis key for this subscription, cycle, and feature
            const usageKey = SUBSCRIPTION_REDIS_KEYS.USAGE(
                subscription.id,
                feature,
                cycle,
            );

            // Get current usage for this cycle
            const redis = this._redisConnection.getConnection();
            const currentUsageStr = await redis.get(usageKey);
            const currentUsage = currentUsageStr
                ? parseInt(currentUsageStr, 10)
                : 0;

            // Check if limit is reached
            const isAllowed = currentUsage < featureLimit;

            return {
                isAllowed,
                currentUsage,
                limit: featureLimit,
                remainingUsage: Math.max(0, featureLimit - currentUsage),
                feature,
            };
        } catch (error) {
            console.error(
                `Error checking feature limit for ${feature}:`,
                error,
            );
            // On error, don't allow to be safe
            return {
                isAllowed: false,
                currentUsage: 0,
                limit: 0,
                remainingUsage: 0,
                feature,
            };
        }
    }

    /**
     * Check if user is allowed to add more products (inventory limit check)
     * @param userId - The user ID to check
     * @returns Promise<FeatureLimitResult>
     */
    async checkInventoryLimit(userId: number): Promise<FeatureLimitResult> {
        try {
            // Get active subscription
            const subscription = await this.getActiveSubscription(userId);

            if (!subscription) {
                return {
                    isAllowed: false,
                    currentUsage: 0,
                    limit: 0,
                    remainingUsage: 0,
                    feature: FEATURES.INVENTORY_PRODUCT,
                };
            }

            // Get full subscription plan details
            const subscriptionPlan =
                await this._subscriptionPlanService.getSubscriptionPlan(
                    subscription.plan.id,
                );

            if (!subscriptionPlan) {
                return {
                    isAllowed: false,
                    currentUsage: 0,
                    limit: 0,
                    remainingUsage: 0,
                    feature: FEATURES.INVENTORY_PRODUCT,
                };
            }

            const inventoryLimit = this.getFeatureLimitFromPlan(
                subscriptionPlan,
                FEATURES.INVENTORY_PRODUCT,
            );

            // If no limit is set (unlimited), allow
            if (!inventoryLimit || inventoryLimit <= 0) {
                return {
                    isAllowed: true,
                    currentUsage: 0,
                    limit: 0,
                    remainingUsage: 0,
                    feature: FEATURES.INVENTORY_PRODUCT,
                };
            }

            // Get current inventory count from InventoryService (not Redis based)
            const currentInventoryCount =
                await this._inventoryService.getInventoriesCountOfUser(userId);

            // Check if limit is reached
            const isAllowed = currentInventoryCount < inventoryLimit;

            return {
                isAllowed,
                currentUsage: currentInventoryCount,
                limit: inventoryLimit,
                remainingUsage: Math.max(
                    0,
                    inventoryLimit - currentInventoryCount,
                ),
                feature: FEATURES.INVENTORY_PRODUCT,
            };
        } catch (error) {
            console.error("Error checking inventory limit:", error);
            // On error, don't allow to be safe
            return {
                isAllowed: false,
                currentUsage: 0,
                limit: 0,
                remainingUsage: 0,
                feature: FEATURES.INVENTORY_PRODUCT,
            };
        }
    }

    /**
     * Helper method to get feature limit from subscription plan
     * @param subscriptionPlan - The subscription plan
     * @param feature - The feature to get limit for
     * @returns The limit for the feature
     */
    private getFeatureLimitFromPlan(
        subscriptionPlan: { botLimit?: number; inventoryLimit?: number },
        feature: FEATURES,
    ): number {
        switch (feature) {
            case FEATURES.CHATBOT_MESSAGE:
                return subscriptionPlan.botLimit || 0;
            case FEATURES.INVENTORY_PRODUCT:
                return subscriptionPlan.inventoryLimit || 0;
            default:
                return 0;
        }
    }

    /**
     * Helper method to check and set notification flag for a subscription cycle
     * @param subscriptionId - The subscription ID
     * @param cycle - The cycle number
     * @param notificationType - The notification type
     * @returns Whether notification should be sent
     */
    private async checkAndSetNotificationFlag(
        subscriptionId: number,
        cycle: number,
        notificationType: string,
    ): Promise<boolean> {
        const redis = this._redisConnection.getConnection();

        const notificationKey = SUBSCRIPTION_REDIS_KEYS.NOTIFICATION(
            subscriptionId,
            cycle,
            notificationType,
        );

        const hasNotified = await redis.get(notificationKey);
        const shouldNotify = hasNotified === null;

        if (shouldNotify) {
            // Use shared TTL constant
            await redis.setex(
                notificationKey,
                SUBSCRIPTION_TTL.NOTIFICATION,
                "1",
            );
        }

        return shouldNotify;
    }

    /**
     * Send SMS notification for bot message limit warning (80% threshold)
     * @param userId - The user ID
     * @param currentUsage - Current usage count
     * @param limit - Total limit
     * @param userLanguage - User's language preference
     */
    async sendBotMessageNearLimitNotification(
        userId: number,
        currentUsage: number,
        limit: number,
        userLanguage: AppLanguage,
    ): Promise<void> {
        try {
            // Get user details
            const user = await this._usersService.getUser(userId);
            if (!user || !user.phone) {
                Logger.warn(
                    "Cannot send SMS notification - user not found or no phone",
                    {
                        userId,
                        action: "sendBotMessageNearLimitNotification",
                    },
                );
                return;
            }

            const remainingMessages = limit - currentUsage;

            // Send SMS using the specific template for near limit
            await this._kavenegarService.sendBotMessageNearLimitNotification(
                user.phone,
                remainingMessages.toString(),
            );

            Logger.info(
                "Bot message near limit SMS notification sent successfully",
                {
                    userId,
                    phone: user.phone,
                    currentUsage,
                    limit,
                    remainingMessages,
                    language: userLanguage,
                    action: "sendBotMessageNearLimitNotification",
                },
            );
        } catch (error) {
            Logger.error(
                "Failed to send bot message near limit SMS notification",
                {
                    userId,
                    currentUsage,
                    limit,
                    error:
                        error instanceof Error ? error.message : String(error),
                    action: "sendBotMessageNearLimitNotification",
                },
            );
        }
    }

    /**
     * Send SMS notification for bot message limit reached
     * @param userId - The user ID
     * @param limit - Total limit
     * @param userLanguage - User's language preference
     */
    async sendBotMessageLimitReachedNotification(
        userId: number,
        limit: number,
        userLanguage: AppLanguage,
    ): Promise<void> {
        try {
            // Get user details
            const user = await this._usersService.getUser(userId);
            if (!user || !user.phone) {
                Logger.warn(
                    "Cannot send SMS notification - user not found or no phone",
                    {
                        userId,
                        action: "sendBotMessageLimitReachedNotification",
                    },
                );
                return;
            }

            // Send SMS using the specific template for limit reached
            await this._kavenegarService.sendBotMessageLimitReachedNotification(
                user.phone,
            );

            Logger.info(
                "Bot message limit reached SMS notification sent successfully",
                {
                    userId,
                    phone: user.phone,
                    limit,
                    language: userLanguage,
                    action: "sendBotMessageLimitReachedNotification",
                },
            );
        } catch (error) {
            Logger.error(
                "Failed to send bot message limit reached SMS notification",
                {
                    userId,
                    limit,
                    error:
                        error instanceof Error ? error.message : String(error),
                    action: "sendBotMessageLimitReachedNotification",
                },
            );
        }
    }

    /**
     * Process bot message limit notifications
     * This method handles both near limit and limit reached notifications
     * @param userId - The user ID
     * @param limitResult - The bot message limit result
     */
    async processBotMessageLimitNotifications(
        userId: number,
        limitResult: BotMessageLimitResult,
    ): Promise<void> {
        try {
            // Get user's language preference
            const userLanguage =
                await this._userLanguageService.getUserLanguage(userId);

            // Send near limit notification (80% threshold)
            if (limitResult.shouldNotifyNearLimit) {
                await this.sendBotMessageNearLimitNotification(
                    userId,
                    limitResult.currentUsage,
                    limitResult.limit,
                    userLanguage,
                );
            }

            // Send limit reached notification
            if (limitResult.shouldNotifyLimitReached) {
                await this.sendBotMessageLimitReachedNotification(
                    userId,
                    limitResult.limit,
                    userLanguage,
                );
            }
        } catch (error) {
            Logger.error("Failed to process bot message limit notifications", {
                userId,
                limitResult,
                error: error instanceof Error ? error.message : String(error),
                action: "processBotMessageLimitNotifications",
            });
        }
    }
}
