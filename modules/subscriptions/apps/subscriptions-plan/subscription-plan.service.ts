import { delay, inject, injectable, registry } from "tsyringe";
import SubscriptionPlanRepo from "./subscription-plan.repo";
import { AddSubscriptionPlanDto } from "./types";
import UserSubscriptionService from "../user-subscriptions/user-subscription.service";
import SubscriptionLimitService, {
    FEATURES,
} from "../subscription-limit/subscription-limit.service";
import { errors, utils } from "../../../common";
import { registries } from "./registries";
import { getSubscriptionPlansSerializer } from "./responses";

@registry(registries)
@injectable()
export default class SubscriptionPlanService {
    constructor(
        private _repo: SubscriptionPlanRepo,
        @inject(delay(() => UserSubscriptionService))
        private _userSubscriptionService: UserSubscriptionService,
        private _subscriptionLimitService: SubscriptionLimitService,
    ) {}

    addSubscriptionPlan = async (args: AddSubscriptionPlanDto) => {
        await this._repo.create(args);
    };

    getSubscriptionPlans = async (parsedQuery: Partial<Express.Query>) => {
        const subscriptionPlans =
            await this._repo.getSubscriptionPlans(parsedQuery);

        return getSubscriptionPlansSerializer(subscriptionPlans);
    };

    getSubscriptionPlan = async (planId: number) => {
        return await this._repo.findOneByQuery({
            id: planId,
        });
    };

    getFreePlan = async () => {
        return await this._repo.findOneByQuery({
            isFree: true,
            isActive: true,
        });
    };

    getCurrentUserSubscription = async (userId: number) => {
        // Get current subscription
        const currentSubscription =
            await this._userSubscriptionService.getCurrentSubscriptionOfUser(
                userId,
            );

        if (utils.isNil(currentSubscription)) {
            return {
                hasSubscription: false,
                plan: null,
                currentUsage: null,
                subscription: null,
            };
        }

        // Get full plan details
        const subscriptionPlan = await this.getSubscriptionPlan(
            currentSubscription.plan.id,
        );

        if (utils.isNil(subscriptionPlan)) {
            throw new errors.NotFoundError("Subscription plan not found");
        }

        // Get current usage for inventory and bot features
        const inventoryLimitResult =
            await this._subscriptionLimitService.checkInventoryLimit(userId);
        const botLimitResult =
            await this._subscriptionLimitService.checkFeatureLimit(
                userId,
                FEATURES.CHATBOT_MESSAGE,
            );

        // Calculate start date based on duration
        const startDate = new Date(currentSubscription.startDate);
        const endDate = new Date(currentSubscription.endDate);
        // Calculate remaining days
        const now = new Date();
        const remainingDays = Math.max(
            0,
            Math.ceil(
                (endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24),
            ),
        );

        const response = {
            hasSubscription: true,
            plan: {
                id: subscriptionPlan.id,
                name: subscriptionPlan.name,
                inventoryLimit: subscriptionPlan.inventoryLimit,
                botLimit: subscriptionPlan.botLimit,
                isFree: subscriptionPlan.isFree,
                price: Number(subscriptionPlan.price),
                month: subscriptionPlan.duration,
                isActive: subscriptionPlan.isActive,
            },
            currentUsage: {
                inventoryUsage: inventoryLimitResult.currentUsage,
                botUsage: botLimitResult.currentUsage,
            },
            subscription: {
                id: currentSubscription.id,
                status: currentSubscription.status,
                startDate: startDate.toISOString(),
                endDate: endDate.toISOString(),
                remainingDays,
            },
        };
        console.log("Current User Subscription Response:", response);

        return response;
    };
}
