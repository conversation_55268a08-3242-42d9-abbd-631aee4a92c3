export interface GetUserSubscriptionResponse {
    id: number;
    status: string;
    endDate: string;
    startDate: string;
    plan: {
        id: number;
        name: string;
        price: string;
        isPopular: boolean;
        duration: number;
    };
}

// Interface for raw database row from getCurrentSubscriptionOfUser
export interface CurrentSubscriptionRow {
    id: number;
    status: string;
    startDate: Date;
    endDate: Date;
    plan: {
        id: number;
        name: string;
        price: string;
        isPopular: boolean;
        duration: number;
    };
}

// Interface for raw database result from getNearestSubscriptionOfUser
export interface RawNearestSubscriptionResult {
    subscription_id: number;
    subscription_status: string;
    subscription_startDate: Date;
    subscription_endDate: Date;
    subscription_planId: number;
    subscription_trackId: string | null;
    subscription_userId: number;
    subscription_description: string | null;
    subscription_rejectionReason: string | null;
    "subscription-plan_id": number;
    "subscription-plan_name": string;
    "subscription-plan_price": string;
    "subscription-plan_duration": number;
    "subscription-plan_isPopular": boolean;
    "subscription-plan_isBase": boolean;
    "subscription-plan_inventoryLimit": number;
    "subscription-plan_botLimit": number;
    "subscription-plan_reportLimit": number;
    "subscription-plan_isActive": boolean;
    "subscription-plan_planLevel": string | null;
    "subscription-plan_isFree": boolean;
}

export interface CreatePaymentResponse {
    trackId: string;
    payLink: string;
}
