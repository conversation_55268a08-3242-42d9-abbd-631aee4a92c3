import { openapi } from "../common";

import CategoryController from "../categories/apps/categories/categories.controller";
import InventoryController from "../inventories/apps/inventories/inventories.controller";
import ChannelController from "../channels/apps/channels/channel.controller";
import UserWebsiteController from "../user-websites/apps/user-websites/user-website.controller";
import UserController from "../users/apps/users/users.controller";
import ChatController from "../chat/apps/chat/chat.controller";
import ClientController from "../users/apps/client/client.controller";
import WebsiteController from "../websites/apps/websites/website.controller";
import OrdersController from "../orders/apps/orders/order.controller";
import RateController from "../rates/apps/rates/rates.controller";
import StaticServerController from "../static-server/apps/static-server/static-server.controller";
import * as base from "./base.json";
import AutomationController from "../automation/apps/automation/automation.controller";
import UserSubscriptionController from "../subscriptions/apps/user-subscriptions/user-subscription.controller";
import SubscriptionPlanController from "../subscriptions/apps/subscriptions-plan/subscription-plan.controller";
import FAQController from "../faq/apps/faq/faq.controller";
import AdminController from "../admin/apps/admin/admin.controller";
import NotificationController from "../notifications/apps/notifications/notification.controller";
import NotificationConfigController from "../notifications/apps/notifications-config/notifications-config.controller";
import HealthController from "../health/apps/health/health.controller";
import ClientInteractionController from "../client-interaction/apps/client-interaction/client-interaction.controller";
import TransactionController from "../transactions/apps/transactions/transaction.controller";

export function initSwagger() {
    const openAPI = new openapi.OpenAPI([
        { prefix: "/users", controller: UserController },
        { prefix: "/users/clients", controller: ClientController },
        { prefix: "/categories", controller: CategoryController },
        { prefix: "/inventories", controller: InventoryController },
        { prefix: "/automations", controller: AutomationController },
        { prefix: "/chats", controller: ChatController },
        { prefix: "/websites", controller: WebsiteController },
        { prefix: "/user-websites", controller: UserWebsiteController },
        { prefix: "/channels", controller: ChannelController },
        { prefix: "/orders", controller: OrdersController },
        { prefix: "/rates", controller: RateController },
        { prefix: "/static-server", controller: StaticServerController },
        {
            prefix: "/subscriptions/plan",
            controller: SubscriptionPlanController,
        },
        { prefix: "/subscriptions", controller: UserSubscriptionController },
        { prefix: "/transactions", controller: TransactionController },
        { prefix: "/faqs", controller: FAQController },
        { prefix: "/admins", controller: AdminController },
        { prefix: "/notifications", controller: NotificationController },
        {
            prefix: "/notifications/config",
            controller: NotificationConfigController,
        },
        { prefix: "/health", controller: HealthController },
        { prefix: "/client-interaction", controller: ClientInteractionController },
    ]);
    const paths = openAPI.generateDoc();
    const swagger = {
        ...base,
        paths,
    };

    return swagger;
}
