import { Channel } from "./entities";

export interface AddChannelDto {
    name: string;
    description: string;
    field: string;
    workingHoursStart?: string;
    workingHoursEnd?: string;
}

export type EditChannelDto = Pick<
    Channel,
    | "description"
    | "field"
    | "workingHoursStart"
    | "workingHoursEnd"
    | "exchangeRateType"
    | "exchangeRateValue"
>;

export interface InstagramSubscribeToEventsDto {
    channelId: number;
    accessToken: string;
    accessTokenExpiresAt: Date;
}

export interface InstagramMessageEntryDto {
    id: string;
    messaging: {
        sender: { id: string };
        recipient: { id: string };
        timestamp: number;
        message: {
            mid: string;
            text?: string; // Optional because Instagram can send messages without text (images, reactions, etc.)
        };
    }[];
}

export interface InstagramMessageEventsDto {
    object: "instagram";
    entry: InstagramMessageEntryDto[];
}
