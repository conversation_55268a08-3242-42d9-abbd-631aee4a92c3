# Subscription System Documentation

## Overview

The subscription system manages user access to features based on their active subscription plan. It enforces feature usage limits, handles subscription lifecycle events (activation, expiration, notifications), and integrates with Redis for usage tracking and BullMQ for scheduled jobs. The system also supports SMS notifications for important subscription events.

---

## Key Concepts

### 1. Subscription Plans
- Defined with properties like `name`, `duration` (months), `inventoryLimit`, `botLimit`, `isFree`, `price`, and `isActive`.
- Managed via the SubscriptionPlanService.
- Users can have a current active subscription plan, which determines their feature limits.

### 2. User Subscriptions
- Each user can have a subscription with a status (`EXECUTED`, `EXPIRED`, etc.), `startDate`, and `endDate`.
- The current active subscription is determined by status and date range.
- Subscriptions are managed and queried via UserSubscriptionService and UserSubscriptionRepo.

### 3. Feature Limits
- Two main features are limited by subscription:
  - `CHATBOT_MESSAGE`: Number of chatbot messages a user can send per cycle.
  - `INVENTORY_PRODUCT`: Number of products a user can add to their inventory.
- Limits are enforced per subscription cycle (monthly, based on plan duration).
- Usage is tracked in Redis, with keys scoped by subscription, feature, and cycle.

### 4. Usage Tracking & Enforcement
- Usage for each feature is incremented/decremented and checked via SubscriptionLimitService.
- Redis is used to store and expire usage counters per cycle.
- If a user exceeds their limit, further usage is blocked until the next cycle.

### 5. Notifications
- **Near Limit Notification:** When usage reaches 80% of the limit, a notification is sent (only once per cycle).
- **Limit Reached Notification:** When the limit is reached, a notification is sent (only once per cycle).
- **Expiry Notification:** Users are notified 3 days before their subscription expires.
- **Expired Notification:** Users are notified when their subscription expires.
- SMS notifications are sent using KavenegarService, with language support.

### 6. Scheduled Jobs
- **Expired Subscriptions Check:** Runs hourly to mark subscriptions as expired if their end date has passed, sends notifications, and cleans up Redis data.
- **Expiry Notification Check:** Runs every 6 hours to notify users whose subscriptions will expire in 3 days.
- Jobs are managed using BullMQ queues and processors in SubscriptionSchedulerService.

### 7. Redis Key Management
- Usage and notification flags are stored in Redis with TTLs matching the subscription cycle or notification window.
- When a subscription expires, all related Redis keys are cleaned up.

---

## Main Services & Responsibilities

- **SubscriptionPlanService:** CRUD for plans, fetches current user plan and usage.
- **SubscriptionLimitService:** Enforces feature limits, tracks usage, handles notifications.
- **SubscriptionSchedulerService:** Schedules and processes background jobs for expiration and notifications, cleans up expired data.

---

## API Endpoints

- **POST `/trigger/expired-check`**: Manually trigger expired subscriptions check.
- **POST `/trigger/expiry-notification`**: Manually trigger expiry notification check.

---

## Usage Flow

1. **User subscribes to a plan:** Plan details and limits are set.
2. **User performs feature actions:** Each action (e.g., sending a chatbot message) checks and updates usage limits.
3. **System tracks usage:** Usage is stored in Redis, scoped by subscription and cycle.
4. **Notifications:** Users are notified as they approach or reach limits, or as their subscription nears expiration.
5. **Scheduled jobs:** Regularly check for expired subscriptions and send notifications.
6. **Cleanup:** When a subscription expires, all related usage and notification data in Redis is removed.

---

## Error Handling

- If any error occurs during limit checks or notifications, the system errs on the side of caution (e.g., blocks further usage or skips notification).
- All errors are logged using the Logger service.

---

## Extensibility

- New features can be added to the `FEATURES` enum and handled in the limit logic.
- Notification templates and logic can be extended for additional events or channels.

---

## Example: Checking and Using a Feature

1. User attempts to send a chatbot message.
2. `SubscriptionLimitService.checkBotMessageLimit(userId)` is called.
3. If allowed, usage is incremented and the action proceeds.
4. If near or at the limit, appropriate notifications are triggered.

---

## Summary

This subscription system provides robust, cycle-based feature limiting, automated notifications, and scheduled maintenance, ensuring users are always aware of their subscription status and usage, and that expired data is efficiently cleaned up.
